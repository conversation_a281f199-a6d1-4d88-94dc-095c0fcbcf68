{"name": "cheff-up-api", "version": "1.0.0", "description": "TypeScript API with filepath-based routing and JWT authentication", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "migrate": "tsx src/scripts/migrate.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["typescript", "api", "jwt", "authentication", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "pg": "^8.11.3", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/node": "^20.10.4", "typescript": "^5.3.3", "tsx": "^4.6.2", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "@types/bun": "latest"}, "private": true}