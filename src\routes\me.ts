import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import { UserModel } from '../models/User';

// Get current user profile - authentication required
export const get = {
  handler: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated'
        };
        return res.status(401).json(response);
      }

      // Get user details from database
      const user = await UserModel.findById(req.user.id);
      
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            surname: user.surname,
            email: user.email,
            created_at: user.created_at,
            updated_at: user.updated_at
          }
        },
        message: 'User profile retrieved successfully'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || 'Failed to retrieve user profile'
      };

      res.status(500).json(response);
    }
  },
  config: {
    requireAuth: true
  }
};
