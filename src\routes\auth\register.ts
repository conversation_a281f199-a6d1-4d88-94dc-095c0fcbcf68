import { Request, Response } from 'express';
import { ApiResponse, RegisterRequest } from '../../types';
import { UserModel } from '../../models/User';
import { validateRequest, registerSchema } from '../../utils/validation';
import { generateToken } from '../../utils/jwt';

// User registration endpoint - no authentication required
export const post = {
  handler: async (req: Request, res: Response) => {
    try {
      // Validate request body
      const validatedData: RegisterRequest = validateRequest(registerSchema, req.body);
      
      // Check if user already exists
      const existingUser = await UserModel.emailExists(validatedData.email);
      if (existingUser) {
        const response: ApiResponse = {
          success: false,
          error: 'User with this email already exists'
        };
        return res.status(409).json(response);
      }

      // Create new user
      const newUser = await UserModel.create(validatedData);
      
      // Generate JWT token
      const token = generateToken({
        id: newUser.id,
        email: newUser.email
      });

      // Remove sensitive data from response
      const { password, ...userResponse } = newUser as any;

      const response: ApiResponse = {
        success: true,
        data: {
          user: userResponse,
          token
        },
        message: 'User registered successfully'
      };

      res.status(201).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || 'Registration failed'
      };

      res.status(400).json(response);
    }
  },
  config: {
    requireAuth: false
  }
};
