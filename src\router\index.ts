import { Router, Request, Response, NextFunction } from 'express';
import { readdirSync, statSync } from 'fs';
import { join, extname, basename } from 'path';
import { RouteHandler, RouteConfig } from '../types';
import { authenticateToken } from '../middleware/auth';

export class FilepathRouter {
  private router: Router;
  private routesPath: string;

  constructor(routesPath: string) {
    this.router = Router();
    this.routesPath = routesPath;
    this.loadRoutes();
  }

  private loadRoutes() {
    this.scanDirectory(this.routesPath, '');
  }

  private scanDirectory(dirPath: string, routePrefix: string) {
    try {
      const items = readdirSync(dirPath);

      for (const item of items) {
        const itemPath = join(dirPath, item);
        const stat = statSync(itemPath);

        if (stat.isDirectory()) {
          // Recursively scan subdirectories
          this.scanDirectory(itemPath, `${routePrefix}/${item}`);
        } else if (stat.isFile() && extname(item) === '.ts') {
          this.loadRouteFile(itemPath, routePrefix);
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }
  }

  private async loadRouteFile(filePath: string, routePrefix: string) {
    try {
      const fileName = basename(filePath, '.ts');

      // Skip index files in subdirectories for route naming
      const routePath = fileName === 'index'
        ? routePrefix || '/'
        : `${routePrefix}/${fileName}`;

      // Convert Windows path to file:// URL for dynamic import
      const fileUrl = process.platform === 'win32'
        ? `file:///${filePath.replace(/\\/g, '/')}`
        : filePath;

      // Dynamic import the route module
      const routeModule = await import(fileUrl);
      
      // Support different HTTP methods
      const methods = ['get', 'post', 'put', 'patch', 'delete'];
      
      for (const method of methods) {
        if (routeModule[method]) {
          this.registerRoute(method, routePath, routeModule[method]);
        }
      }

      // Support default export
      if (routeModule.default) {
        this.registerRoute('get', routePath, routeModule.default);
      }

    } catch (error) {
      console.error(`Error loading route file ${filePath}:`, error);
    }
  }

  private registerRoute(method: string, path: string, handler: RouteHandler | Function) {
    const middleware: any[] = [];
    let routeHandler: Function;
    let config: RouteConfig = { requireAuth: true }; // Default to requiring auth

    // Check if handler is a RouteHandler object or just a function
    if (typeof handler === 'function') {
      routeHandler = handler;
    } else if (handler && typeof handler.handler === 'function') {
      routeHandler = handler.handler;
      config = { ...config, ...handler.config };
    } else {
      console.error(`Invalid handler for ${method.toUpperCase()} ${path}`);
      return;
    }

    // Add authentication middleware if required
    if (config.requireAuth) {
      middleware.push(authenticateToken);
    }

    // Register the route
    (this.router as any)[method](path, ...middleware, routeHandler);
    
    console.log(`Registered route: ${method.toUpperCase()} ${path} ${config.requireAuth ? '(protected)' : '(public)'}`);
  }

  public getRouter(): Router {
    return this.router;
  }
}

export default FilepathRouter;
