import { Request, Response } from 'express';
import { ApiResponse, LoginRequest } from '../../types';
import { UserModel } from '../../models/User';
import { validateRequest, loginSchema } from '../../utils/validation';
import { generateToken } from '../../utils/jwt';

// User login endpoint - no authentication required
export const post = {
  handler: async (req: Request, res: Response) => {
    try {
      // Validate request body
      const validatedData: LoginRequest = validateRequest(loginSchema, req.body);
      
      // Validate user credentials
      const user = await UserModel.validatePassword(validatedData.email, validatedData.password);
      
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email or password'
        };
        return res.status(401).json(response);
      }

      // Update last login timestamp
      await UserModel.updateLastLogin(user.id);
      
      // Generate JWT token
      const token = generateToken({
        id: user.id,
        email: user.email
      });

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            surname: user.surname,
            email: user.email,
            created_at: user.created_at,
            updated_at: user.updated_at
          },
          token
        },
        message: 'Login successful'
      };

      res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || 'Login failed'
      };

      res.status(400).json(response);
    }
  },
  config: {
    requireAuth: false
  }
};
