import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types';

export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', error);

  const response: ApiResponse = {
    success: false,
    error: error.message || 'Internal server error'
  };

  // Handle specific error types
  if (error.message.includes('duplicate key value violates unique constraint')) {
    response.error = 'Email already exists';
    return res.status(409).json(response);
  }

  if (error.message.includes('invalid input syntax')) {
    response.error = 'Invalid data format';
    return res.status(400).json(response);
  }

  // Default to 500 for unhandled errors
  res.status(500).json(response);
};

export const notFoundHandler = (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: false,
    error: `Route ${req.method} ${req.path} not found`
  };
  res.status(404).json(response);
};
