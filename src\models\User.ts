import pool from '../config/database';
import { User, RegisterRequest } from '../types';
import { hashPassword, comparePassword } from '../utils/password';

export class UserModel {
  static async create(userData: RegisterRequest): Promise<User> {
    const { name, surname, email, password } = userData;
    
    // Hash the password
    const hashedPassword = await hashPassword(password);
    
    const query = `
      INSERT INTO users (name, surname, email, password)
      VALUES ($1, $2, $3, $4)
      RETURNING id, name, surname, email, created_at, updated_at
    `;
    
    const values = [name, surname, email, hashedPassword];
    const result = await pool.query(query, values);
    
    return result.rows[0];
  }

  static async findByEmail(email: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await pool.query(query, [email]);
    
    return result.rows[0] || null;
  }

  static async findById(id: number): Promise<User | null> {
    const query = 'SELECT id, name, surname, email, created_at, updated_at FROM users WHERE id = $1';
    const result = await pool.query(query, [id]);
    
    return result.rows[0] || null;
  }

  static async validatePassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email);
    
    if (!user) {
      return null;
    }
    
    const isValid = await comparePassword(password, user.password);
    
    if (!isValid) {
      return null;
    }
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  static async updateLastLogin(id: number): Promise<void> {
    const query = 'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1';
    await pool.query(query, [id]);
  }

  static async emailExists(email: string): Promise<boolean> {
    const query = 'SELECT 1 FROM users WHERE email = $1';
    const result = await pool.query(query, [email]);
    
    return result.rows.length > 0;
  }
}
