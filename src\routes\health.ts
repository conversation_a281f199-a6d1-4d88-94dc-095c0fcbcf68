import { Request, Response } from 'express';
import { ApiResponse } from '../types';
import pool from '../config/database';

// Health check endpoint - no authentication required
export const get = {
  handler: async (req: Request, res: Response) => {
    try {
      // Check database connection
      const dbResult = await pool.query('SELECT NOW()');
      const dbStatus = dbResult.rows.length > 0 ? 'connected' : 'disconnected';
      
      const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: dbStatus,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      };

      const response: ApiResponse = {
        success: true,
        data: healthData,
        message: 'Service is healthy'
      };

      res.status(200).json(response);
    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Health check failed',
        data: {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          database: 'disconnected'
        }
      };

      res.status(503).json(response);
    }
  },
  config: {
    requireAuth: false
  }
};
