import { Request } from 'express';

export interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  password: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserPayload {
  id: number;
  email: string;
}

export interface AuthenticatedRequest extends Request {
  user?: UserPayload;
}

export interface RegisterRequest {
  name: string;
  surname: string;
  email: string;
  password: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RouteConfig {
  requireAuth?: boolean;
}

export interface RouteHandler {
  handler: Function;
  config?: RouteConfig;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}
